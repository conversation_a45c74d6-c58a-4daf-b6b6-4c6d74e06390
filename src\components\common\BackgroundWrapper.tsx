import React from 'react';
import { useResponsiveBackground } from '@/hooks/useResponsiveBackground';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { getBackgroundConfig, BackgroundImageKey } from '@/utils/backgroundImages';

interface BackgroundWrapperProps {
  /** Clé de l'image de fond à utiliser */
  backgroundKey: BackgroundImageKey;
  /** Contenu à afficher */
  children: React.ReactNode;
  /** Opacité de l'overlay (par défaut: 0.3) */
  overlayOpacity?: number;
  /** Classe CSS supplémentaire pour le conteneur */
  className?: string;
  /** Activer le scroll interne (par défaut: true) */
  enableScroll?: boolean;
  /** Forcer le scroll en haut au montage (par défaut: false) */
  scrollToTop?: boolean;
  /** Indique s'il y a un header en haut (par défaut: true) */
  hasHeader?: boolean;
}

/**
 * Composant wrapper pour les pages avec image de fond responsive
 * Garantit qu'aucun ascenseur n'apparaît sur la page
 */
export const BackgroundWrapper: React.FC<BackgroundWrapperProps> = ({
  backgroundKey,
  children,
  overlayOpacity = 0.3,
  className = '',
  enableScroll = true,
  scrollToTop = false,
  hasHeader = true
}) => {
  // Configuration de l'image de fond
  const backgroundConfig = getBackgroundConfig(backgroundKey);
  const { backgroundStyles, isReady } = useResponsiveBackground(backgroundConfig);

  // Détection d'appareil et d'orientation
  const deviceInfo = useDeviceDetection();

  // Référence pour le conteneur de scroll
  const scrollContainerRef = React.useRef<HTMLDivElement>(null);

  // Vérifier si un flou doit être appliqué
  const shouldBlur = (backgroundConfig as any).blur === true;

  // Calculer le padding-bottom automatique selon l'appareil et l'orientation
  const getAutoPaddingBottom = (): string => {
    // Mode portrait (hauteur > largeur)
    const isPortrait = deviceInfo.screenHeight > deviceInfo.screenWidth;

    if (deviceInfo.isMobile) {
      // Smartphone : padding important en mode portrait
      return isPortrait ? 'pb-32' : 'pb-16';
    } else if (deviceInfo.isTablet) {
      // Tablette : padding moyen en mode portrait, léger en paysage
      return isPortrait ? 'pb-24' : 'pb-12';
    } else {
      // Desktop : padding minimal
      return 'pb-6';
    }
  };

  // Forcer le scroll en haut si demandé
  React.useEffect(() => {
    if (scrollToTop && enableScroll && scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [scrollToTop, enableScroll]);

  return (
    <div
      className={`fixed ${hasHeader ? 'top-16' : 'top-0'} left-0 right-0 bottom-0 overflow-hidden ${className}`}
      style={backgroundStyles}
    >
      {/* Couche de flou si nécessaire */}
      {shouldBlur && (
        <div
          className="absolute inset-0 z-5"
          style={{
            ...backgroundStyles,
            filter: 'blur(2px)',
            transform: 'scale(1.1)' // Évite les bordures noires du flou
          }}
        />
      )}

      {/* Overlay pour améliorer la lisibilité */}
      <div
        className="absolute inset-0 bg-black z-10"
        style={{ opacity: overlayOpacity }}
      />

      {/* Contenu principal */}
      <div
        ref={scrollContainerRef}
        className={`relative z-20 h-full ${enableScroll ? 'overflow-y-auto' : 'overflow-hidden'}`}
      >
        {/* Wrapper avec padding automatique */}
        <div className={`min-h-full ${getAutoPaddingBottom()}`}>
          {children}
        </div>
      </div>
    </div>
  );
};
